-- Debug script to test dropdown functionality
print("=== Dropdown Debug Script ===")

-- Mock LocalPlayer and Backpack for testing
local MockBackpack = {
    GetChildren = function()
        return {
            -- Simulate different types of seed tools
            {
                Name = "Blueberry Seed",
                ClassName = "Tool",
                IsA = function(self, className) return className == "Tool" end,
                FindFirstChild = function(self, name)
                    if name == "Plant_Name" then
                        return { Value = "Blueberry" }
                    elseif name == "Numbers" then
                        return { Value = 5 }
                    end
                    return nil
                end,
                GetChildren = function()
                    return {
                        { Name = "Plant_Name", ClassName = "StringValue", Value = "Blueberry" },
                        { Name = "Numbers", ClassName = "IntValue", Value = 5 }
                    }
                end
            },
            {
                Name = "Carrot Seed",
                ClassName = "Tool", 
                IsA = function(self, className) return className == "Tool" end,
                FindFirstChild = function(self, name)
                    if name == "Plant_Name" then
                        return { Value = "Carrot" }
                    elseif name == "Numbers" then
                        return { Value = 3 }
                    end
                    return nil
                end,
                GetChildren = function()
                    return {
                        { Name = "Plant_Name", ClassName = "StringValue", Value = "Carrot" },
                        { Name = "Numbers", ClassName = "IntValue", Value = 3 }
                    }
                end
            },
            {
                -- Simulate a seed without proper structure
                Name = "Simple Seed",
                ClassName = "Tool",
                IsA = function(self, className) return className == "Tool" end,
                FindFirstChild = function(self, name) return nil end,
                GetChildren = function() return {} end
            },
            {
                -- Non-seed tool
                Name = "Watering Can",
                ClassName = "Tool",
                IsA = function(self, className) return className == "Tool" end,
                FindFirstChild = function(self, name) return nil end,
                GetChildren = function() return {} end
            }
        }
    end
}

local MockCharacter = {
    GetChildren = function() return {} end
}

-- Test functions
local function DebugSeedTool(tool)
    print("DEBUG: Inspecting tool:", tool.Name)
    print("  - ClassName:", tool.ClassName)
    print("  - Children:")
    for _, child in pairs(tool:GetChildren()) do
        print("    -", child.Name, "(" .. child.ClassName .. ")")
        if child.Value then
            print("      Value:", child.Value)
        end
    end
end

local function GetSeedInfo(Seed)
    -- Debug the first seed we encounter
    if Seed.Name:find("Seed") then
        DebugSeedTool(Seed)
    end
    
    local PlantName = Seed:FindFirstChild("Plant_Name")
    local Count = Seed:FindFirstChild("Numbers")
    
    if not PlantName then 
        print("DEBUG: No Plant_Name found in", Seed.Name)
        return 
    end

    print("DEBUG: Found Plant_Name:", PlantName.Value, "Count:", Count and Count.Value or "nil")
    return PlantName.Value, Count and Count.Value or 1
end

local function CollectSeedsFromParent(Parent, Seeds)
    for _, Tool in pairs(Parent:GetChildren()) do
        local Name, Count = GetSeedInfo(Tool)
        if not Name then continue end

        Seeds[Name] = {
            Count = Count,
            Tool = Tool
        }
    end
end

local function GetOwnedSeeds()
    local OwnedSeeds = {}
    
    CollectSeedsFromParent(MockBackpack, OwnedSeeds)
    if MockCharacter then
        CollectSeedsFromParent(MockCharacter, OwnedSeeds)
    end

    return OwnedSeeds
end

local function GetSeedNamesForDropdown()
    local seedNames = {}
    
    -- First try the reference method
    local ownedSeeds = GetOwnedSeeds()
    for plantName, seedData in pairs(ownedSeeds) do
        if seedData.Count and seedData.Count > 0 then
            seedNames[plantName] = seedData.Count -- Show count for reference
        end
    end
    
    -- If no seeds found with reference method, use fallback method
    if next(seedNames) == nil then
        print("DEBUG: No seeds found with reference method, trying fallback...")
        
        -- Check backpack for any tools with "Seed" in name
        for _, tool in pairs(MockBackpack:GetChildren()) do
            if tool:IsA("Tool") and tool.Name:find("Seed") then
                local seedName = tool.Name:gsub(" Seed", ""):gsub("Seed", "") -- Remove "Seed" from name
                if seedName == "" then seedName = tool.Name end -- If nothing left, use full name
                
                -- Count how many of this seed we have
                local count = 0
                for _, checkTool in pairs(MockBackpack:GetChildren()) do
                    if checkTool:IsA("Tool") and checkTool.Name == tool.Name then
                        count = count + 1
                    end
                end
                
                seedNames[seedName] = count
                print("DEBUG: Found seed:", seedName, "Count:", count, "Tool name:", tool.Name)
            end
        end
    end
    
    print("DEBUG: Final seed list for dropdown:")
    for name, count in pairs(seedNames) do
        print("  -", name, "(" .. count .. ")")
    end
    
    return seedNames
end

-- Run tests
print("\n=== Testing GetOwnedSeeds ===")
local ownedSeeds = GetOwnedSeeds()
print("Found", #ownedSeeds, "owned seeds:")
for name, data in pairs(ownedSeeds) do
    print("  -", name, "Count:", data.Count, "Tool:", data.Tool.Name)
end

print("\n=== Testing GetSeedNamesForDropdown ===")
local dropdownSeeds = GetSeedNamesForDropdown()
local count = 0
for name, seedCount in pairs(dropdownSeeds) do
    count = count + 1
end
print("Found", count, "seeds for dropdown:")
for name, seedCount in pairs(dropdownSeeds) do
    print("  -", name, "(" .. seedCount .. ")")
end

print("\n=== Debug Complete ===")
